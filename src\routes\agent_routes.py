import logging
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query, Depends, Request, status as fastapi_status
from datetime import datetime
# from redis import Redis # Removed
# from rq import Queue as RQ_Queue # Removed

from schemas.agent_schemas import (
    <PERSON><PERSON><PERSON>,
    AgentUpdate,
    AgentResponse,
    AgentList,
    AgentSummary,
    AgentReaction,
    DialogueResponse,
    ReactionResponse
)
from services import agent_service
from utils.llm_utils import validate_api_key
# from utils.date_utils import format_datetime # Not used directly here anymore for memory timestamp
from utils.logging_utils import log_route_details # Added
# from config import REDIS_URL, DEFAULT_QUEUE_NAME # Removed as task queue is removed
# from tasks.agent_memory_tasks import process_add_memory_for_agent # Removed as task queue is removed

router = APIRouter()
logger = logging.getLogger(__name__)

# Removed Redis connection and RQ Queue initialization block as it's no longer needed.
# task_queue = None # Ensure task_queue is not referenced later, or remove references.

def verify_api_key():
    if not validate_api_key():
        raise HTTPException(
            status_code=500,
            detail="OpenAI API key is not configured. Please set the OPENAI_API_KEY environment variable."
        )
    return True

@router.post("/", response_model=AgentResponse, summary="Create a new agent")
@log_route_details(logger) # Added
async def create_agent(request: Request, agent: AgentCreate, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to create agent with name: {agent.name} for user_id: {agent.user_id}") # Covered by decorator
    """
    Create a new generative agent. Initial memories will be processed synchronously.

    - **name**: The character's name
    - **age**: The optional age of the character
    - **traits**: Permanent traits to ascribe to the character
    - **status**: The traits of the character you wish not to change
    # - **llm_model**: The language model to use (optional) # Removed
    - **reflection_threshold**: When to trigger reflection (optional)
    - **importance_weight**: Weight for memory importance (optional)
    - **verbose**: Enable verbose logging (optional)
    """
    # try: # Decorator handles try/except for general errors and HTTPExceptions
    
    # Agent creation and initial memory processing will now be synchronous.
    created_agent_data = agent_service.create_agent_and_process_initial_memories_sync(
        name=agent.name,
        age=agent.age,
        traits=agent.traits,
        status=agent.status,
        initial_memories=agent.initial_memories, # Pass initial memories directly
        reflection_threshold=agent.reflection_threshold,
        importance_weight=agent.importance_weight,
        verbose=agent.verbose,
        user_id=agent.user_id
    )

    agent_id = created_agent_data["id"]

    # Log message updated to reflect synchronous processing
    if agent.initial_memories:
        logger.info(f"Agent '{created_agent_data['name']}' (ID: {agent_id}) created and {len(agent.initial_memories)} initial memories processed synchronously.")
    else:
        logger.info(f"Agent '{created_agent_data['name']}' (ID: {agent_id}) created synchronously with no initial memories.")
    
    return AgentResponse(
        id=agent_id,
        name=created_agent_data["name"],
        age=created_agent_data.get("age"),
        traits=created_agent_data["traits"],
        status=created_agent_data["status"],
        summary=created_agent_data.get("summary", ""),
        last_refreshed=created_agent_data.get("last_refreshed", datetime.now())
    )
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error creating agent: {e}", exc_info=True) # Covered by decorator
    #     # Ensure to use fastapi_status for status codes
    #     raise HTTPException(status_code=fastapi_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create agent: {str(e)}") # Covered by decorator


@router.get("/", response_model=AgentList, summary="List all agents")
@log_route_details(logger) # Added
async def list_agents(request: Request, user_id: Optional[str] = None): # Added request
    # logger.info(f"Attempting to list agents for user_id: {user_id if user_id else 'all users'}") # Covered by decorator
    """
    List all available generative agents for a user.

    - **user_id**: ID of the user to list agents for (optional)
    """
    # try: # Covered by decorator
    agents = agent_service.list_agents(user_id=user_id)

    agent_responses = [
        AgentResponse(
            id=agent["id"],
            name=agent["name"],
            age=agent["age"],
            traits=agent["traits"],
            status=agent["status"],
            summary=agent["summary"],
            last_refreshed=agent["last_refreshed"]
        )
        for agent in agents
    ]
    logger.info(f"Successfully listed {len(agent_responses)} agents for user_id: {user_id if user_id else 'all users'}.") # Keep specific success log
    return AgentList(agents=agent_responses)
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error listing agents: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail=f"Failed to list agents: {str(e)}") # Covered by decorator


@router.get("/{agent_id}", response_model=AgentResponse, summary="Get agent by ID")
@log_route_details(logger) # Added
async def get_agent(request: Request, agent_id: str, user_id: Optional[str] = None): # Added request
    # logger.info(f"Attempting to get agent with ID: {agent_id} for user_id: {user_id}") # Covered by decorator
    """
    Get a specific generative agent by ID.

    - **agent_id**: The unique identifier for the agent
    - **user_id**: ID of the user who owns the agent (optional)
    """
    agent_data = agent_service.get_agent(agent_id, user_id)
    if not agent_data:
        # logger.warning(f"Agent with ID {agent_id} not found for user_id: {user_id}.") # Decorator will log HTTPException
        raise HTTPException(status_code=404, detail=f"Agent with ID {agent_id} not found")
    logger.info(f"Successfully retrieved agent '{agent_data['name']}' (ID: {agent_id}).") # Keep specific success log
    return AgentResponse(
        id=agent_data["id"],
        name=agent_data["name"],
        age=agent_data["age"],
        traits=agent_data["traits"],
        status=agent_data["status"],
        summary=agent_data["summary"],
        last_refreshed=agent_data["last_refreshed"]
    )


@router.put("/{agent_id}", response_model=AgentResponse, summary="Update agent")
@log_route_details(logger) # Added
async def update_agent(request: Request, agent_id: str, agent: AgentUpdate, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to update agent with ID: {agent_id} for user_id: {agent.user_id}") # Covered by decorator
    """
    Update an existing generative agent.

    - **agent_id**: The unique identifier for the agent
    - **age**: New age for the agent (optional)
    - **traits**: New traits for the agent (optional)
    - **status**: New status for the agent (optional)
    # - **llm_model**: New language model to use (optional) # Removed
    - **reflection_threshold**: New reflection threshold (optional)
    - **importance_weight**: New importance weight (optional)
    - **verbose**: New verbose setting (optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    updated_agent = agent_service.update_agent(
        agent_id=agent_id,
        age=agent.age,
        traits=agent.traits,
        status=agent.status,
        # llm_model=agent.llm_model, # Removed
        reflection_threshold=agent.reflection_threshold,
        importance_weight=agent.importance_weight,
        verbose=agent.verbose,
        user_id=agent.user_id
    )

    if not updated_agent:
        # logger.warning(f"Agent with ID {agent_id} not found for update by user_id: {agent.user_id}.") # Decorator will log HTTPException
        raise HTTPException(status_code=404, detail=f"Agent with ID {agent_id} not found")
    logger.info(f"Successfully updated agent '{updated_agent['name']}' (ID: {agent_id}).") # Keep specific success log
    return AgentResponse(
        id=updated_agent["id"],
        name=updated_agent["name"],
        age=updated_agent["age"],
        traits=updated_agent["traits"],
        status=updated_agent["status"],
        summary=updated_agent["summary"],
            last_refreshed=updated_agent["last_refreshed"]
        )
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error updating agent: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail=f"Failed to update agent: {str(e)}") # Covered by decorator


@router.delete("/{agent_id}", summary="Delete agent")
@log_route_details(logger) # Added
async def delete_agent(request: Request, agent_id: str, user_id: Optional[str] = None): # Added request
    # logger.info(f"Attempting to delete agent with ID: {agent_id} for user_id: {user_id}") # Covered by decorator
    """
    Delete a generative agent.

    - **agent_id**: The unique identifier for the agent
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    success = agent_service.delete_agent(agent_id, user_id)
    if not success:
        # logger.warning(f"Agent with ID {agent_id} not found for deletion by user_id: {user_id}.") # Decorator will log HTTPException
        raise HTTPException(status_code=404, detail=f"Agent with ID {agent_id} not found")
    logger.info(f"Successfully deleted agent with ID: {agent_id}.") # Keep specific success log
    return {"message": f"Agent with ID {agent_id} deleted successfully"}
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error deleting agent: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail=f"Failed to delete agent: {str(e)}") # Covered by decorator


@router.get("/{agent_id}/summary", response_model=AgentSummary, summary="Get agent summary")
@log_route_details(logger) # Added
async def get_agent_summary(
    request: Request, # Added
    agent_id: str,
    force_refresh: bool = Query(False, description="Force refresh of the summary"),
    current_time: Optional[str] = Query(None, description="Current time (ISO format)"),
    user_id: Optional[str] = Query(None, description="ID of the user who owns the agent")
):
    # logger.info(f"Attempting to get summary for agent ID: {agent_id}, force_refresh: {force_refresh}, user_id: {user_id}") # Covered by decorator
    """
    Get a summary of a generative agent.

    - **agent_id**: The unique identifier for the agent
    - **force_refresh**: Whether to force a refresh of the summary
    - **current_time**: The current time (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    summary_data = agent_service.get_agent_summary(
        agent_id=agent_id,
        force_refresh=force_refresh,
        current_time=current_time,
        user_id=user_id
    )

    if not summary_data:
        # logger.warning(f"Agent with ID {agent_id} not found for summary retrieval by user_id: {user_id}.") # Decorator will log HTTPException
        raise HTTPException(status_code=404, detail=f"Agent with ID {agent_id} not found")
    logger.info(f"Successfully retrieved summary for agent ID: {agent_id}.") # Keep specific success log
    return AgentSummary(
        summary=summary_data["summary"],
        last_refreshed=summary_data["last_refreshed"]
    )
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error getting agent summary: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail=f"Failed to get agent summary: {str(e)}") # Covered by decorator


@router.post("/{agent_id}/react", response_model=ReactionResponse, summary="Generate agent reaction")
@log_route_details(logger) # Added
async def generate_reaction(request: Request, agent_id: str, reaction_req: AgentReaction, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to generate reaction for agent ID: {agent_id}, observation: '{reaction_req.observation[:50]}...', user_id: {reaction_req.user_id}") # Covered by decorator
    """
    Generate a reaction from an agent based on an observation.

    - **agent_id**: The unique identifier for the agent
    - **observation**: The observation for the agent to react to
    - **current_time**: The current time (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    reaction_result = agent_service.generate_reaction(
        agent_id=agent_id,
        observation=reaction_req.observation,
        current_time=reaction_req.current_time,
        user_id=reaction_req.user_id
    )

    if reaction_result is None:
        # logger.warning(f"Agent with ID {agent_id} not found for reaction generation by user_id: {reaction_req.user_id}.") # Decorator will log HTTPException
        raise HTTPException(status_code=404, detail=f"Agent with ID {agent_id} not found")

    is_dialogue, reaction = reaction_result
    logger.info(f"Successfully generated reaction for agent ID: {agent_id}. Is dialogue: {is_dialogue}, Reaction: '{str(reaction)[:50]}...'") # Keep specific success log
    return ReactionResponse(
        is_dialogue=is_dialogue,
        reaction=reaction
    )
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error generating reaction: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail=f"Failed to generate reaction: {str(e)}") # Covered by decorator


@router.post("/{agent_id}/dialogue", response_model=DialogueResponse, summary="Generate dialogue response")
@log_route_details(logger) # Added
async def generate_dialogue(request: Request, agent_id: str, reaction_req: AgentReaction, _: bool = Depends(verify_api_key)): # Added request
    # logger.info(f"Attempting to generate dialogue for agent ID: {agent_id}, observation: '{reaction_req.observation[:50]}...', user_id: {reaction_req.user_id}") # Covered by decorator
    """
    Generate a dialogue response from an agent based on an observation.

    - **agent_id**: The unique identifier for the agent
    - **observation**: The observation for the agent to respond to
    - **current_time**: The current time (ISO format, optional)
    - **user_id**: ID of the user who owns the agent (optional)
    """
    # try: # Covered by decorator
    dialogue_result = agent_service.generate_dialogue_response(
        agent_id=agent_id,
        observation=reaction_req.observation,
        current_time=reaction_req.current_time,
        user_id=reaction_req.user_id
    )

    if dialogue_result is None:
        # logger.warning(f"Agent with ID {agent_id} not found for dialogue generation by user_id: {reaction_req.user_id}.") # Decorator will log HTTPException
        raise HTTPException(status_code=404, detail=f"Agent with ID {agent_id} not found")

    is_continuing, response = dialogue_result
    logger.info(f"Successfully generated dialogue for agent ID: {agent_id}. Is continuing: {is_continuing}, Response: '{str(response)[:50]}...'") # Keep specific success log
    return DialogueResponse(
        is_dialogue_continuing=is_continuing,
        response=response
    )
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Error generating dialogue: {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail=f"Failed to generate dialogue: {str(e)}") # Covered by decorator
