// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "AgentBridgeSettings.generated.h"

/**
 * Configuration settings for the AgentBridge plugin.
 * These settings can be edited in Project Settings > Plugins > Agent Bridge
 */
UCLASS(config = Game, defaultconfig, meta = (DisplayName = "Agent Bridge"))
class AGENTBRIDGE_API UAgentBridgeSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UAgentBridgeSettings();

    /** The base URL for the backend service API */
    UPROPERTY(config, EditAnywhere, Category = "API", meta = (DisplayName = "Service URL"))
    FString ServiceUrl;

    /** Enable verbose logging for requests and responses */
    UPROPERTY(config, EditAnywhere, Category = "Debugging", meta = (DisplayName = "Enable Verbose Logging"))
    bool bEnableVerboseLogging;

    /** Request timeout in seconds */
    UPROPERTY(config, EditAnywhere, Category = "API", meta = (DisplayName = "Request Timeout (seconds)", ClampMin = "1", ClampMax = "300"))
    int32 RequestTimeoutSeconds;

    /** Enable automatic retry for failed requests */
    UPROPERTY(config, EditAnywhere, Category = "API", meta = (DisplayName = "Enable Request Retry"))
    bool bEnableRequestRetry;

    /** Maximum number of retry attempts */
    UPROPERTY(config, EditAnywhere, Category = "API", meta = (DisplayName = "Max Retry Attempts", ClampMin = "1", ClampMax = "10", EditCondition = "bEnableRequestRetry"))
    int32 MaxRetryAttempts;

    /** Base delay between retry attempts in seconds */
    UPROPERTY(config, EditAnywhere, Category = "API", meta = (DisplayName = "Retry Base Delay (seconds)", ClampMin = "0.1", ClampMax = "30.0", EditCondition = "bEnableRequestRetry"))
    float RetryBaseDelaySeconds;

    //~ Begin UDeveloperSettings Interface
    virtual FName GetCategoryName() const override;
    virtual FText GetSectionText() const override;
#if WITH_EDITOR
    virtual FText GetSectionDescription() const override;
#endif
    //~ End UDeveloperSettings Interface
};
